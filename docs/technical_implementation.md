# Technical Implementation Details

## Core Processing Logic

### Lambda Handler

The Lambda handler in `index.js` is the entry point for the function. It:

1. Parses the SQS message to extract `userId` and `s3Key`
2. Retrieves data from S3 using the `getDataFromS3` function
3. Loads static data using `getStaticData`
4. Creates an OpenSearch client using `createClient`
5. Processes logs using the `processLogs` function
6. Returns a response with appropriate status code and message

```mermaid
flowchart TD
    A[Lambda Invocation] --> B[Parse SQS Message]
    B --> C{Valid userId & s3Key?}
    C -->|No| D[Return 400 Error]
    C -->|Yes| E[Get Data from S3]
    E --> F[Load Static Data]
    F --> G[Create OpenSearch Client]
    G --> H[Process Logs]
    H --> I[Return Success Response]
    H -->|Error| J[Return 500 Error]
```

### Log Processing

The `processLogs` function in `index.js` is responsible for processing the logs:

1. Gets static sources data
2. Maps tracker IDs to logs
3. Processes each tracker's logs in parallel
4. For each log entry:
   - Checks if it's incremental data
   - Upserts or inserts the log into OpenSearch
   - Handles special case for BGM logs (also inserting into CGM index)
5. Triggers target computation via SQS
6. Updates last synced values for sources

```mermaid
flowchart TD
    A[processLogs] --> B[Get Static Sources]
    B --> C[Initialize Response Object]
    C --> D[Process Each Tracker in Parallel]
    D --> E[For Each Log Entry with Rate Limiting]
    E --> F{Is Incremental Data?}
    F -->|Yes| G{Log Exists?}
    G -->|Yes| H[Update Log]
    G -->|No| I[Insert New Log]
    F -->|No| J[Upsert Log]
    E --> K{Is BGM Tracker?}
    K -->|Yes| L[Also Insert into CGM Index]
    D --> M[Trigger Target Computation]
    M --> N[Update Last Synced Values]
```

## Data Storage and Retrieval

### OpenSearch Operations

The system uses OpenSearch for data storage and retrieval:

1. **Connection Management**:
   - Creates authenticated connections to AWS OpenSearch
   - Implements credential refreshing every 10 minutes
   - Provides connection pooling and reuse

2. **Data Operations**:
   - `upsertData`: Updates existing logs or inserts new ones
   - `insertData`: Inserts new logs
   - `updateLogById`: Updates logs by ID
   - `getLogByTimestampByDeviceId`: Retrieves logs by timestamp and device ID
   - `getDataByLogId`: Retrieves logs by ID

3. **Retry Logic**:
   - Uses `executeWithRetry` for resilient OpenSearch operations
   - Configurable retry count and backoff

## Special Processing Cases

### Incremental Data Handling

For sources with incremental data:

1. Checks if a log with the same timestamp and device ID exists
2. If it exists, updates the existing log
3. If it doesn't exist, inserts a new log

### BGM to CGM Conversion

Blood Glucose Meter (BGM) logs are also inserted into the Continuous Glucose Monitor (CGM) index:

1. Processes BGM logs normally
2. Also inserts the same data into the CGM index
3. Tracks the inserted CGM log IDs

### Exercise and Mindfulness Logs

Exercise and mindfulness logs have special handling:

1. Processes logs normally
2. Also calls specialized functions in `exerciseLogs.js` and `mindfulnessLogs.js`
3. Only triggers these specialized functions for default devices

## Target Computation

After processing logs, the system triggers target computation:

1. Checks which logs are eligible for target computation
2. Groups logs by date
3. Sends messages to the Target Computation SQS queue
4. Each message contains:
   - User ID
   - Date
   - Tracker ID to device ID mapping
   - Timestamp

```mermaid
flowchart TD
    A[postLogUpsert] --> B[Get Default Trackers]
    B --> C[Get UTC Offset]
    C --> D[Process Each Log]
    D --> E{Is Default Device or Manual Entry?}
    E -->|Yes| F[Add to Target Computation List]
    E -->|No| G[Skip]
    F --> H[Group by Date]
    H --> I[Send SQS Messages]
```

## Rate Limiting

The system uses Bottleneck for rate limiting:

1. Configures a limiter with:
   - `maxConcurrent`: 5 concurrent operations
   - `minTime`: 100ms between operations
2. Wraps log processing operations with the limiter
3. Prevents overwhelming OpenSearch with too many concurrent requests

## Error Handling

The system implements comprehensive error handling:

1. **Try-Catch Blocks**:
   - Around all critical operations
   - With detailed error logging

2. **Retry Logic**:
   - For OpenSearch operations
   - With configurable retry count and backoff

3. **Graceful Degradation**:
   - Continues processing other logs if one fails
   - Returns partial success when possible

## Logging

The system uses Bunyan for structured logging:

1. **Log Levels**:
   - Info: Normal operations
   - Warn: Potential issues
   - Error: Critical failures

2. **Log Content**:
   - Operation description
   - Input parameters
   - Error details
   - Timing information

## Performance Optimizations

1. **Parallel Processing**:
   - Uses Promise.all for concurrent processing
   - Processes different trackers in parallel
   - Processes logs within a tracker in parallel (with rate limiting)

2. **Connection Reuse**:
   - Reuses OpenSearch connections
   - Refreshes credentials periodically

3. **Efficient Queries**:
   - Uses targeted queries with appropriate filters
   - Uses bulk operations where possible

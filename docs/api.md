# API Documentation

## Lambda Function: TrackersIngestion

This Lambda function processes health and fitness tracking data from various sources and stores it in OpenSearch.

### Input Event Structure

The Lambda function expects an SQS event with the following structure:

```json
{
  "Records": [
    {
      "body": "{\"userId\":\"user123\",\"s3Key\":\"path/to/data.json\"}"
    }
  ]
}
```

Where:
- `userId`: The ID of the user whose data is being processed
- `s3Key`: The S3 key where the tracking data is stored

### S3 Data Structure

The data in S3 should have the following structure:

```json
{
  "trackerId1": [
    {
      "userId": "user123",
      "deviceId": "device456",
      "sourceId": 1,
      "timestamp": "2023-01-01T12:00:00Z",
      "logId": "log789",
      "field1": "value1",
      "field2": "value2"
    }
  ],
  "trackerId2": [
    {
      "userId": "user123",
      "deviceId": "device456",
      "sourceId": 2,
      "timestamp": "2023-01-01T12:00:00Z",
      "logId": "log790",
      "field3": "value3",
      "field4": "value4"
    }
  ]
}
```

Where:
- `trackerId`: The ID of the tracker (e.g., 2 for water, 5 for sleep)
- Each array contains log entries for that tracker
- Each log entry must include:
  - `userId`: The ID of the user
  - `deviceId`: The ID of the device that generated the log
  - `sourceId`: The ID of the data source
  - `timestamp`: The timestamp of the log
  - Additional fields specific to the tracker type

### Response Structure

The Lambda function returns a response with the following structure:

#### Success Response

```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "message": "Request processed successfully",
    "data": {
      "trackerId1": ["logId1", "logId2"],
      "trackerId2": ["logId3"]
    }
  },
  "headers": {
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
    "Access-Control-Allow-Origin": "*"
  }
}
```

Where:
- `data`: Contains the IDs of the inserted/updated logs for each tracker

#### Error Response - Invalid Input

```json
{
  "statusCode": 400,
  "body": {
    "success": false,
    "message": "Invalid event data"
  },
  "headers": {
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
    "Access-Control-Allow-Origin": "*"
  }
}
```

#### Error Response - Processing Error

```json
{
  "statusCode": 500,
  "body": {
    "success": false,
    "message": "Failed to process request"
  },
  "headers": {
    "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
    "Access-Control-Allow-Origin": "*"
  }
}
```

## OpenSearch Indices

The Lambda function stores data in various OpenSearch indices based on the tracker type:

| Tracker ID | Description | Index Name |
|------------|-------------|------------|
| 2 | Water | water |
| 3 | Activity Summary | activity_summary |
| 4 | Activity | activity |
| 5 | Sleep | sleep |
| 6 | Blood Pressure | bp |
| 7 | Blood Glucose | bg |
| 8 | CGM | egvs |
| 9 | SpO2 | spo2 |
| 10 | Heart Rate | heart_rate |
| 11 | HRV | hrv |
| 12 | VO2 | vo2 |
| 13 | ECG | ecg |
| 14 | Height | height |
| 15 | Weight | weight |
| 16 | Fat | fat |
| 17 | BMI | bmi |
| 18 | Temperature | temp |
| 19 | Waist Size | waist_size |
| 20 | Hip Size | hip_size |
| 21 | Chest Size | chest_size |
| 22 | Arm Size | arm_size |
| 23 | Quad Size | quad_size |
| 24 | Mindfulness | mindfulness |
| 25 | Resting Heart Rate | resting_heart_rate |

## Target Computation SQS Messages

After processing logs, the Lambda function sends messages to the Target Computation SQS queue with the following structure:

```json
{
  "userId": "user123",
  "date": "2023-01-01",
  "trackerIdDeviceIdMapping": {
    "2": "device456",
    "5": "device789"
  },
  "time": 1672567200000
}
```

Where:
- `userId`: The ID of the user
- `date`: The date for which to compute targets
- `trackerIdDeviceIdMapping`: A mapping of tracker IDs to device IDs
- `time`: A timestamp to uniquely identify the message

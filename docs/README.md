# TrackersIngestion Documentation

## Overview

This documentation provides comprehensive information about the TrackersIngestion Lambda function, which is the first component in the trackers asynchronous pipeline. It processes health and fitness tracking data from various sources, stores it in OpenSearch, and triggers downstream target computation processes.

## Documentation Structure

- [Architecture](./architecture.md): System architecture, component breakdown, and data flow
- [Project](./project.md): Project structure, key components, and dependencies
- [Technical Implementation](./technical_implementation.md): Detailed implementation details and processing logic
- [API](./api.md): API documentation, input/output formats, and OpenSearch indices
- [Deployment](./deployment.md): Deployment guide, required AWS resources, and scaling considerations

## Quick Start

### System Overview

TrackersIngestion is a serverless AWS Lambda function that:

1. Receives messages from SQS containing a userId and s3Key
2. Retrieves tracking data from S3
3. Processes the data and stores it in OpenSearch
4. Triggers target computation via SQS

```mermaid
graph TD
    A[Client Applications] -->|Upload Data| B[S3 Bucket]
    B -->|Trigger| C[SQS Queue]
    C -->|Process Message| D[TrackersIngestion Lambda]
    D -->|Store Data| E[OpenSearch]
    D -->|Trigger Target Computation| F[Target Computation SQS]
```

### Key Components

- **Lambda Handler**: Entry point for the Lambda function
- **Log Processing Service**: Core functionality for processing and storing log data
- **OpenSearch Connection**: Manages connections to OpenSearch
- **AWS Utilities**: Handles interactions with AWS services

### Supported Tracker Types

The system supports multiple health and fitness trackers, each with a dedicated OpenSearch index:

| Tracker ID | Description | Index Name |
|------------|-------------|------------|
| 2 | Water | water |
| 3 | Activity Summary | activity_summary |
| 4 | Activity | activity |
| 5 | Sleep | sleep |
| 6 | Blood Pressure | bp |
| 7 | Blood Glucose | bg |
| 8 | CGM | egvs |
| 9 | SpO2 | spo2 |
| 10 | Heart Rate | heart_rate |
| 11-25 | Various | Various |

## Development

### Prerequisites

- Node.js 14.x or later
- AWS account with appropriate permissions
- AWS CLI configured with appropriate credentials

### Local Testing

```bash
# Install dependencies
npm install

# Run tests
npm test
```

### Deployment

See the [Deployment Guide](./deployment.md) for detailed instructions.

## Contributing

When contributing to this project, please:

1. Follow the existing code style
2. Write tests for new functionality
3. Update documentation as needed
4. Run tests before submitting changes

## License

This project is licensed under the ISC License - see the LICENSE file for details.

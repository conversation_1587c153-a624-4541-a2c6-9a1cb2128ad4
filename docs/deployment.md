# Deployment Guide

## Prerequisites

Before deploying the TrackersIngestion Lambda function, ensure you have:

1. **AWS Account**: With appropriate permissions to create and manage:
   - Lambda functions
   - S3 buckets
   - SQS queues
   - OpenSearch domains
   - IAM roles and policies

2. **AWS CLI**: Configured with appropriate credentials

3. **Node.js**: Version 14.x or later

4. **Dependencies**: All required npm packages installed

## Deployment Methods

### Method 1: AWS CodeBuild (Recommended)

The project includes buildspec files for automated deployment using AWS CodeBuild:

#### Production Deployment

1. Set up a CodeBuild project using the `buildspec.yml` file
2. Configure the following environment variables:
   - `AWS_REGION`: The AWS region to deploy to
   - `S3_BUCKET`: The S3 bucket for deployment artifacts
   - `LAMBDA_FUNCTION_NAME`: The name of the Lambda function
   - `OS_HOST`: The OpenSearch endpoint
   - `TARGET_COMPUTATION_SQS_URL`: The URL of the target computation SQS queue

3. Trigger the build to deploy to production

```mermaid
flowchart TD
    A[CodeBuild Project] --> B[Install Dependencies]
    B --> C[Run Tests]
    C --> D[Package Lambda Function]
    D --> E[Upload to S3]
    E --> F[Update Lambda Function]
```

#### Development Deployment

1. Set up a CodeBuild project using the `devbuildspec.yml` file
2. Configure the same environment variables as for production, but with development values
3. Trigger the build to deploy to the development environment

### Method 2: Manual Deployment

#### Step 1: Package the Lambda Function

```bash
# Install dependencies
npm install

# Create a deployment package
zip -r function.zip . -x "*.git*" "node_modules/aws-sdk/*" "test/*"
```

#### Step 2: Create or Update the Lambda Function

```bash
# Create a new Lambda function
aws lambda create-function \
  --function-name TrackersIngestion \
  --runtime nodejs14.x \
  --handler index.handler \
  --role arn:aws:iam::ACCOUNT_ID:role/lambda-role \
  --zip-file fileb://function.zip

# Or update an existing Lambda function
aws lambda update-function-code \
  --function-name TrackersIngestion \
  --zip-file fileb://function.zip
```

#### Step 3: Configure Environment Variables

```bash
aws lambda update-function-configuration \
  --function-name TrackersIngestion \
  --environment "Variables={NODE_ENV=production,OS_HOST=https://your-opensearch-endpoint.amazonaws.com,TARGET_COMPUTATION_SQS_URL=https://sqs.region.amazonaws.com/account/queue.fifo}"
```

## Required AWS Resources

### 1. Lambda Function

- **Name**: TrackersIngestion
- **Runtime**: Node.js 14.x
- **Handler**: index.handler
- **Memory**: 512 MB (adjust based on workload)
- **Timeout**: 30 seconds (adjust based on workload)
- **Concurrency**: As needed for your workload

### 2. IAM Role

The Lambda function requires an IAM role with the following permissions:

- **AWSLambdaBasicExecutionRole**: For CloudWatch Logs
- **S3 Access**: To read from the S3 bucket
- **SQS Access**: To send messages to the target computation queue
- **OpenSearch Access**: To read from and write to OpenSearch indices

Example policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::your-bucket/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "sqs:SendMessage"
      ],
      "Resource": "arn:aws:sqs:region:account:target-computation.fifo"
    },
    {
      "Effect": "Allow",
      "Action": [
        "es:ESHttpGet",
        "es:ESHttpPost",
        "es:ESHttpPut",
        "es:ESHttpDelete"
      ],
      "Resource": "arn:aws:es:region:account:domain/your-domain/*"
    }
  ]
}
```

### 3. SQS Trigger

Configure an SQS trigger for the Lambda function:

1. Create an SQS queue for incoming messages
2. Configure the Lambda function to be triggered by this queue
3. Set the batch size and maximum batch window as needed

### 4. Target Computation SQS Queue

Create a FIFO SQS queue for target computation:

1. Name: target-computation.fifo
2. Content-based deduplication: Enabled
3. Visibility timeout: At least as long as the downstream Lambda function's timeout

### 5. OpenSearch Domain

Ensure you have an OpenSearch domain with the required indices:

1. Create the indices listed in the API documentation
2. Configure appropriate mappings for each index
3. Set up access policies to allow the Lambda function to access the domain

## Monitoring and Logging

After deployment, set up monitoring and logging:

1. **CloudWatch Logs**: Review logs for errors and performance issues
2. **CloudWatch Metrics**: Monitor Lambda execution time, error rate, and concurrency
3. **CloudWatch Alarms**: Set up alarms for error thresholds and performance degradation
4. **X-Ray**: Enable X-Ray tracing for detailed performance analysis

## Scaling Considerations

The Lambda function scales automatically based on the number of SQS messages, but consider:

1. **Concurrency Limits**: Set appropriate reserved concurrency to prevent overwhelming OpenSearch
2. **Rate Limiting**: The function uses Bottleneck for rate limiting, adjust parameters as needed
3. **Memory Allocation**: Increase memory allocation for better performance with large batches
4. **Timeout**: Set an appropriate timeout based on expected processing time

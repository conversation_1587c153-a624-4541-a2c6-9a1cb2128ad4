# Project Folder Structure

The TrackersIngestion project has the following folder structure:

```mermaid
graph TD
    A[TrackersIngestion] --> B[environment]
    A --> C[service]
    A --> D[utils]
    A --> E[test]
    A --> F[docs]
    A --> G[.memory]
    A --> H[index.js]
    A --> I[package.json]
    A --> J[buildspec.yml]
    A --> K[devbuildspec.yml]
    A --> L[.gitignore]
    
    B --> B1[development.js]
    B --> B2[index.js]
    B --> B3[production.js]
    B --> B4[production_test.js]
    
    C --> C1[devices.js]
    C --> C2[logs.js]
    C --> C3[trackers.js]
    C --> C4[userProfile.js]
    C --> C5[users.js]
    C --> C6[utils.js]
    
    D --> D1[aws.js]
    D --> D2[connection.js]
    D --> D3[elasticSearchOperations.js]
    D --> D4[exerciseLogs.js]
    D --> D5[helpers.js]
    D --> D6[logger.js]
    D --> D7[mindfulnessLogs.js]
    D --> D8[staticData.js]
    
    F --> F1[README.md]
    F --> F2[architecture.md]
    F --> F3[project.md]
    F --> F4[technical_implementation.md]
    F --> F5[api.md]
    F --> F6[deployment.md]
    F --> F7[folder_structure.md]
    
    G --> G1[memory.md]
```

## Key Directories

### 1. environment/

Contains environment-specific configuration files:

- `development.js`: Development environment settings
- `production.js`: Production environment settings
- `production_test.js`: Test production environment settings
- `index.js`: Environment loader that selects the appropriate configuration

### 2. service/

Contains service layer components:

- `devices.js`: Device management service
- `logs.js`: Log processing service
- `trackers.js`: Tracker management service
- `userProfile.js`: User profile service
- `users.js`: User management service
- `utils.js`: Service utilities

### 3. utils/

Contains utility functions:

- `aws.js`: AWS service interactions
- `connection.js`: OpenSearch connection management
- `elasticSearchOperations.js`: OpenSearch operations
- `exerciseLogs.js`: Exercise log processing
- `helpers.js`: Helper functions
- `logger.js`: Logging utility
- `mindfulnessLogs.js`: Mindfulness log processing
- `staticData.js`: Static data management

### 4. docs/

Contains project documentation:

- `README.md`: Overview and documentation structure
- `architecture.md`: System architecture with Mermaid diagrams
- `project.md`: Project structure and components
- `technical_implementation.md`: Detailed implementation details
- `api.md`: API documentation and data formats
- `deployment.md`: Deployment guide and AWS resources
- `folder_structure.md`: This file, describing the project structure

### 5. .memory/

Contains memory files for tracking changes:

- `memory.md`: Records of changes made to the project

### 6. Root Files

- `index.js`: Lambda entry point
- `package.json`: Project dependencies
- `buildspec.yml`: AWS CodeBuild specification for production
- `devbuildspec.yml`: AWS CodeBuild specification for development
- `.gitignore`: Git ignore file

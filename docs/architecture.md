# TrackersIngestion Architecture

## Overview

TrackersIngestion is a serverless AWS Lambda function designed to process health and fitness tracking data from various sources. It serves as the first lightweight Lambda in the trackers asynchronous pipeline, responsible for ingesting data from S3, processing it, and storing it in OpenSearch indices.

## System Architecture

```mermaid
graph TD
    A[Client Applications] -->|Upload Data| B[S3 Bucket]
    B -->|Trigger| C[SQS Queue]
    C -->|Process Message| D[TrackersIngestion Lambda]
    D -->|Store Data| E[OpenSearch]
    D -->|Trigger Target Computation| F[Target Computation SQS]
    F -->|Process| G[Target Computation Lambda]
    
    subgraph "Data Processing Pipeline"
        D
        H[Static Data Cache]
        D <-->|Fetch/Cache| H
    end
```

## Component Breakdown

### 1. AWS Services Used

- **S3**: Stores the raw tracking data uploaded by client applications
- **SQS**: Queues messages for processing by the Lambda function
- **Lambda**: Executes the TrackersIngestion code
- **OpenSearch**: Stores processed tracking data in various indices
- **IAM**: Manages permissions and authentication

### 2. Core Components

#### Lambda Handler (index.js)
- Entry point for the Lambda function
- Parses the SQS message to extract userId and s3Key
- Retrieves data from S3
- Processes logs through the processLogs function
- Returns appropriate responses

#### Data Processing (processLogs)
- Maps tracker data to appropriate indices
- Processes each log entry with rate limiting using Bottleneck
- Handles incremental data updates
- Updates last synced values for sources
- Triggers target computation via SQS

#### OpenSearch Connection (utils/connection.js)
- Creates and manages connections to OpenSearch
- Handles AWS authentication
- Implements credential refreshing

### 3. Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant S3
    participant SQS
    participant Lambda
    participant OpenSearch
    participant TargetSQS

    Client->>S3: Upload tracking data
    S3->>SQS: Trigger message
    SQS->>Lambda: Process message
    Lambda->>S3: Fetch data
    Lambda->>OpenSearch: Get static data
    Lambda->>OpenSearch: Process & store logs
    Lambda->>TargetSQS: Trigger target computation
    Lambda->>OpenSearch: Update last synced values
```

## Data Model

### Tracker Types
The system supports multiple health and fitness trackers, each with a dedicated OpenSearch index:

```mermaid
graph TD
    Trackers[Trackers] --> T1[Water]
    Trackers --> T2[Activity]
    Trackers --> T3[Sleep]
    Trackers --> T4[Blood Pressure]
    Trackers --> T5[Blood Glucose]
    Trackers --> T6[SpO2]
    Trackers --> T7[Heart Rate]
    Trackers --> T8[HRV]
    Trackers --> T9[VO2]
    Trackers --> T10[ECG]
    Trackers --> T11[Body Measurements]
    Trackers --> T12[Mindfulness]
```

### OpenSearch Indices
Each tracker type has a dedicated OpenSearch index for storing its data:

| Tracker ID | Description | Index Name |
|------------|-------------|------------|
| 2 | Water | water |
| 3 | Activity Summary | activity_summary |
| 4 | Activity | activity |
| 5 | Sleep | sleep |
| 6 | Blood Pressure | bp |
| 7 | Blood Glucose | bg |
| 8 | CGM | egvs |
| 9 | SpO2 | spo2 |
| 10 | Heart Rate | heart_rate |
| 11 | HRV | hrv |
| 12 | VO2 | vo2 |
| 13 | ECG | ecg |
| 14-23 | Body Measurements | Various |
| 24 | Mindfulness | mindfulness |
| 25 | Resting Heart Rate | resting_heart_rate |

## Error Handling and Resilience

The system implements several resilience patterns:

1. **Retry Logic**: Uses executeWithRetry for OpenSearch operations
2. **Rate Limiting**: Uses Bottleneck to prevent overwhelming downstream services
3. **Error Logging**: Comprehensive logging for troubleshooting
4. **Graceful Degradation**: Continues processing other logs if one fails

## Deployment

The service is deployed as an AWS Lambda function with the following configurations:

- **Runtime**: Node.js
- **Memory**: Configured based on workload requirements
- **Timeout**: Set to accommodate processing time for large batches
- **Environment Variables**: Configured for different environments (dev, production)

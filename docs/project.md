# TrackersIngestion Project Documentation

## Project Overview

TrackersIngestion is a Node.js-based AWS Lambda function that serves as the first component in the trackers asynchronous pipeline. It is responsible for processing health and fitness tracking data from various sources, storing it in OpenSearch, and triggering downstream target computation processes.

## Project Structure

```
TrackersIngestion/
├── environment/            # Environment configuration
│   ├── development.js      # Development environment settings
│   ├── index.js            # Environment loader
│   ├── production.js       # Production environment settings
│   └── production_test.js  # Test production environment settings
├── service/                # Service layer components
│   ├── devices.js          # Device management service
│   ├── logs.js             # Log processing service
│   ├── trackers.js         # Tracker management service
│   ├── userProfile.js      # User profile service
│   ├── users.js            # User management service
│   └── utils.js            # Service utilities
├── utils/                  # Utility functions
│   ├── aws.js              # AWS service interactions
│   ├── connection.js       # OpenSearch connection management
│   ├── elasticSearchOperations.js # OpenSearch operations
│   ├── exerciseLogs.js     # Exercise log processing
│   ├── helpers.js          # Helper functions
│   ├── logger.js           # Logging utility
│   ├── mindfulnessLogs.js  # Mindfulness log processing
│   └── staticData.js       # Static data management
├── test/                   # Test files
├── index.js                # Lambda entry point
├── package.json            # Project dependencies
└── buildspec.yml           # AWS CodeBuild specification
```

## Key Components

### 1. Lambda Handler (index.js)

The main entry point for the Lambda function. It:
- Receives events from SQS
- Extracts userId and s3Key from the message
- Retrieves data from S3
- Processes logs through the processLogs function
- Returns appropriate responses

```mermaid
flowchart TD
    A[Lambda Invocation] --> B{Valid Event?}
    B -->|Yes| C[Fetch Data from S3]
    B -->|No| D[Return 400 Error]
    C --> E[Load Static Data]
    E --> F[Create OpenSearch Client]
    F --> G[Process Logs]
    G --> H[Return Success Response]
    G -->|Error| I[Return 500 Error]
```

### 2. Log Processing Service (service/logs.js)

Handles the core functionality of processing and storing log data:
- Maps tracker types to OpenSearch indices
- Provides methods for querying, inserting, and updating logs
- Handles special cases for different tracker types
- Triggers target computation for eligible logs

### 3. OpenSearch Connection (utils/connection.js)

Manages connections to OpenSearch:
- Creates authenticated connections to AWS OpenSearch
- Implements credential refreshing
- Provides connection pooling and reuse

### 4. AWS Utilities (utils/aws.js)

Handles interactions with AWS services:
- Retrieves data from S3
- Sends messages to SQS for target computation

## Data Flow

```mermaid
sequenceDiagram
    participant SQS as SQS Queue
    participant Lambda as TrackersIngestion Lambda
    participant S3 as S3 Bucket
    participant OS as OpenSearch
    participant TargetSQS as Target Computation SQS

    SQS->>Lambda: Trigger with message
    Lambda->>S3: Get data from S3Key
    S3-->>Lambda: Return tracking data
    Lambda->>OS: Get static data
    OS-->>Lambda: Return static data
    
    loop For each tracker type
        loop For each log entry (rate limited)
            Lambda->>OS: Check if log exists
            OS-->>Lambda: Return existing log (if any)
            alt Log exists
                Lambda->>OS: Update log
            else Log doesn't exist
                Lambda->>OS: Insert new log
            end
        end
    end
    
    Lambda->>TargetSQS: Send target computation messages
    Lambda->>OS: Update last synced values
```

## Configuration

The project uses a hierarchical configuration system with environment-specific settings:

1. **Base Configuration** (environment/index.js):
   - Defines common settings and OpenSearch indices
   - Sets up configuration loading

2. **Environment-Specific Configuration**:
   - Development (environment/development.js)
   - Production (environment/production.js)
   - Production Test (environment/production_test.js)

## Deployment

The project is deployed as an AWS Lambda function using AWS CodeBuild:

1. **buildspec.yml**: Defines the build and deployment process for production
2. **devbuildspec.yml**: Defines the build and deployment process for development

## Testing

Tests are located in the `test/` directory and can be run using:

```bash
npm test
```

## Dependencies

Key dependencies include:

- **@opensearch-project/opensearch**: For OpenSearch interactions
- **aws-sdk**: For AWS service interactions
- **bottleneck**: For rate limiting
- **bunyan**: For logging
- **nconf**: For configuration management

## Error Handling

The system implements comprehensive error handling:

1. **Retry Logic**: Uses executeWithRetry for OpenSearch operations
2. **Rate Limiting**: Uses Bottleneck to prevent overwhelming downstream services
3. **Logging**: Detailed logging for troubleshooting
4. **Graceful Degradation**: Continues processing other logs if one fails

## Performance Considerations

The system is designed for performance:

1. **Parallel Processing**: Uses Promise.all for concurrent processing
2. **Rate Limiting**: Prevents overwhelming downstream services
3. **Connection Reuse**: Reuses OpenSearch connections
4. **Credential Caching**: Caches AWS credentials with periodic refreshing

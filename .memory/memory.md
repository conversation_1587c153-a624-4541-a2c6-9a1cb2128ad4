# Project Documentation Memory

## Changes Made

### 2023-05-13

Created comprehensive documentation for the TrackersIngestion project:

1. Created docs folder structure
2. Created the following documentation files:
   - `docs/README.md`: Overview and documentation structure
   - `docs/architecture.md`: System architecture with Mermaid diagrams
   - `docs/project.md`: Project structure and components
   - `docs/technical_implementation.md`: Detailed implementation details
   - `docs/api.md`: API documentation and data formats
   - `docs/deployment.md`: Deployment guide and AWS resources

### 2023-05-14

Refactored the environment configuration system:

1. Created environment/env/ directory structure
2. Created .env files for each environment:
   - `.env.development`: Development environment variables
   - `.env.sandbox`: Sandbox environment variables
   - `.env.production`: Production environment variables
   - `.env.example`: Example environment variables template
3. Installed dotenv package for loading environment variables
4. Updated environment configuration files:
   - Modified environment/index.js to use dotenv
   - Updated environment-specific JS files to use process.env
   - Renamed production_test.js to sandbox.js
5. Updated .gitignore to exclude .env files
6. Created scripts/upload-env-to-secrets.sh for uploading environment variables to AWS Secrets Manager
7. Added npm scripts for running with different environments
8. Created environment/README.md to document the environment configuration changes

### Documentation Structure

The documentation is organized as follows:

1. **Architecture Documentation**:
   - System overview
   - Component breakdown
   - Data flow diagrams using Mermaid
   - Data model

2. **Project Documentation**:
   - Project structure
   - Key components
   - Configuration
   - Dependencies

3. **Technical Implementation**:
   - Core processing logic
   - Data storage and retrieval
   - Special processing cases
   - Error handling
   - Performance optimizations

4. **API Documentation**:
   - Input/output formats
   - OpenSearch indices
   - SQS message formats

5. **Deployment Guide**:
   - Deployment methods
   - Required AWS resources
   - Monitoring and scaling

### Mermaid Diagrams

Created the following Mermaid diagrams:

1. System Architecture:
   - Client → S3 → SQS → Lambda → OpenSearch/Target SQS

2. Component Breakdown:
   - Lambda Handler flow
   - Data Processing flow

3. Data Flow:
   - Sequence diagram of the entire process

4. Tracker Types:
   - Hierarchical diagram of supported tracker types

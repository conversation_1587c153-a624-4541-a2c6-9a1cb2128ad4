const lambdaLocal = require("lambda-local");
const handler = require("../index"); // Adjust this line if needed
const bodyJson = require("./testcase.json");
const event = {
  Records: [{ body: JSON.stringify(bodyJson) }],
};

lambdaLocal.execute({
  event,
  lambdaFunc: handler,
  lambdaHandler: "handler", // Ensure this matches the exported function name
  timeoutMs: 2 ** 31 - 1, // Increase from default 3000
  callback: (err, data) => {
    if (err) {
      console.error(err);
    } else {
      console.log(data);
      return data;
    }
  },
});

const { trackersService } = require("./trackers");

async function getDefaultDeviceIdByTrackerId(userId, trackerId) {
  let resp = await trackersService.getAllDefaultTrackers(userId);
  let defaultDevice = resp?.defaultDevices?.filter(x => x.trackerId == trackerId)?.[0]

  if (defaultDevice) {
    return defaultDevice?.deviceId;
  }
  return null;
}

const devicesService = {
  getDefaultDeviceIdByTrackerId,
};

module.exports = {
  devicesService,
};

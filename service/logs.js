const { getClient } = require("../utils/connection");
const { log: logger } = require("../utils/logger");
const utils = require("../service/utils");
const { trackersService } = require("./trackers");
const { getLocalDateString } = require("../utils/helpers");
const { sendTargetComputationSQSMessage } = require("../utils/aws");
const { getUTCOffsetValue } = require("../service/userProfile");
const { devicesService } = require("./devices");
const { executeWithRetry, executeBulkWithRetry, parseOpenSearchError } = require("../utils/elasticSearchOperations");
const exerciseLogs = require("../utils/exerciseLogs");
const mindfulnessLogs = require("../utils/mindfulnessLogs");
const { config } = require("../environment/index");
const MANUAL_ENTRY_DEVICE_ID = -1;
const sleepLogTrackerId = 5; // sleep log tracker id

const trackerMap = {
  1: {
    // meal log: all the operations like meallog CRUD & target computation will happen in meal log service
    indexName: null,
  },
  2: {
    indexName: config.INDEX.water,
  },
  3: {
    indexName: config.INDEX.activitySummary,
  },
  4: {
    indexName: config.INDEX.activity,
  },
  5: {
    indexName: config.INDEX.sleep,
  },
  6: {
    indexName: config.INDEX.bp,
  },
  7: {
    indexName: config.INDEX.bg,
  },
  8: {
    indexName: config.INDEX.egvs,
  },
  9: {
    indexName: config.INDEX.spo2,
  },
  10: {
    indexName: config.INDEX.heartRate,
  },
  11: {
    indexName: config.INDEX.hrv,
  },
  12: {
    indexName: config.INDEX.vo2,
  },
  13: {
    indexName: config.INDEX.ecg,
  },
  14: {
    indexName: config.INDEX.height,
  },
  15: {
    indexName: config.INDEX.weight,
  },
  16: {
    indexName: config.INDEX.fat,
  },
  17: {
    indexName: config.INDEX.bmi,
  },
  18: {
    indexName: config.INDEX.temp,
  },
  19: {
    indexName: config.INDEX.waistSize,
  },
  20: {
    indexName: config.INDEX.hipSize,
  },
  21: {
    indexName: config.INDEX.chestSize,
  },
  22: {
    indexName: config.INDEX.armSize,
  },
  23: {
    indexName: config.INDEX.quadSize,
  },
  24: {
    indexName: config.INDEX.mindfulness,
  },
  25: {
    indexName: config.INDEX.restingHeartRate,
  },
};

async function getLogByTimestampByDeviceId(userId, indexName, timestamp, deviceId) {
  try {
    const response = await executeWithRetry(
      async (client) => {
        return await client.search({
          index: indexName,
          body: {
            query: {
              bool: {
                must: [
                  { match: { timestamp } },
                  { match: { "userId.keyword": userId } },
                  { match: { deviceId } },
                ],
              },
            },
          },
        });
      },
      {
        description: `getLogByTimestampByDeviceId from ${indexName}`,
        maxRetries: 3,
      }
    );

    const doc = response.body?.hits?.hits?.[0];
    return doc ? { id: doc._id, ...doc._source } : null;
  } catch (error) {
    const errorInfo = parseOpenSearchError(error);

    logger.warn(`Failed to get log by timestamp from ${indexName}`, {
      indexName,
      userId,
      deviceId,
      timestamp,
      error: errorInfo.message,
      errorType: errorInfo.type,
      statusCode: errorInfo.statusCode,
      details: errorInfo.details,
      rootCause: errorInfo.rootCause,
      isRetryable: errorInfo.isRetryable
    });
    return null;
  }
}

async function getDataByLogId(indexName, userId, logId) {
  try {
    const response = await executeWithRetry(
      async (client) => {
        return await client.search({
          index: indexName,
          body: {
            query: {
              bool: {
                must: [
                  { match: { _id: logId } },
                  { match: { "userId.keyword": userId } },
                ],
              },
            },
          },
        });
      },
      {
        description: `getDataByLogId from ${indexName}`,
        maxRetries: 3,
      }
    );

    const data = response.body?.hits?.hits[0] || null;
    return data ? { id: data._id, ...data._source } : null;
  } catch (error) {
    const errorInfo = parseOpenSearchError(error);

    logger.warn(`Failed to get log by ID from ${indexName}`, {
      indexName,
      userId,
      logId,
      error: errorInfo.message,
      errorType: errorInfo.type,
      statusCode: errorInfo.statusCode,
      details: errorInfo.details,
      rootCause: errorInfo.rootCause,
      isRetryable: errorInfo.isRetryable
    });
    return null;
  }
}

async function upsertData(trackerId, indexName, document) {
  try {
    switch (Number(trackerId)) {
      case 4: // exercise_logs
        await exerciseLogs.addLog(document.userId, document);
        break;
      case 24: // mindfulness_logs
        await mindfulnessLogs.addLog(document.userId, document);
        break;
    }
    let mustConditions = [
      { match: { "userId.keyword": document.userId } },
      { match: { deviceId: document.deviceId } },
    ];

    if (document?.logId && document?.logId !== null) {
      mustConditions.push({ match: { "logId.keyword": document.logId } });
    } else {
      // Though sleep log will have logId, there can be case where data from manual entry, apple, etc may not have logId for sleep log
      let comparator =
        Number(trackerId) == sleepLogTrackerId ? "startTime" : "timestamp";
      const bufferMinutes = 1;
      const timestamp = new Date(document[comparator]);
      const timeBuffer = bufferMinutes * 60 * 1000;

      mustConditions.push({
        range: {
          [comparator]: {
            gte: new Date(timestamp.getTime() - timeBuffer).toISOString(),
            lte: new Date(timestamp.getTime() + timeBuffer).toISOString(),
          },
        },
      });
    }
    const response = await executeWithRetry(
      async (client) => {
        return await client.search({
          index: indexName,
          body: {
            query: {
              bool: {
                must: mustConditions,
              },
            },
          },
        });
      },
      {
        description: `upsertData() search with mustConditions in ${indexName}`,
        maxRetries: 3,
      }
    );

    const id = response.body?.hits?.hits[0]?._id || null;

    let insertedId = null;
    if (id) {
      const updatedLog = await executeWithRetry(
        async (client) => {
          return await client.update({
            index: indexName,
            id,
            body: {
              doc: { ...document },
            },
          });
        },
        {
          description: `updateDocumentWithRetry in ${indexName}`,
          maxRetries: 3,
        }
      );
      insertedId = updatedLog.body?._id;
    } else {
      insertedId = await insertData(trackerId, indexName, document);
    }
    if (insertedId) {
      return insertedId;
    }
  } catch (error) {
    logger.warn(`Failed to upsert log in ${indexName} for tracker ${trackerId}, document ID ${document.logId || document.timestamp}: ${error.message}`);
  }
}

async function insertData(trackerId, indexName, document) {
  let id;
  // For exercise & mindfulness, target computation will happen in respective repos
  switch (Number(trackerId)) {
    case 4: {
      // exercise_logs
      const defaultDeviceId =
        await devicesService.getDefaultDeviceIdByTrackerId(
          document.userId,
          trackerId
        );
      if (defaultDeviceId && defaultDeviceId == document.deviceId) {
        await exerciseLogs.addLog(document.userId, document);
      }
      id = await utils.insertData(indexName, document);
      break;
    }
    case 24: {
      // mindfulness_logs
      const defaultDeviceId =
        await devicesService.getDefaultDeviceIdByTrackerId(
          document.userId,
          trackerId
        );
      if (defaultDeviceId && defaultDeviceId == document.deviceId) {
        await mindfulnessLogs.addLog(document.userId, document);
      }
      id = await utils.insertData(indexName, document);
      break;
    }
    default:
      id = await utils.insertData(indexName, document);
      break;
  }
  return id;
}

async function updateLogById(trackerId, indexName, document, userId, logId) {
  try {
    const client = getClient();
    const data = await getDataByLogId(indexName, userId, logId);

    if (data) {
      const updatedLog = await client.update({
        index: indexName,
        id: logId,
        body: { doc: { ...document } },
      });
      // Log update & then target computation will happen in respective repos
      switch (Number(trackerId)) {
        case 4: // exercise_logs
          await exerciseLogs.addLog(document.userId, document);
          break;
        case 24: // mindfulness_logs
          await mindfulnessLogs.addLog(document.userId, document);
          break;
        default:
          delete data?.id;
          break;
      }
      return updatedLog.body?._id;
    }
  } catch (error) {
    logger.warn(`Failed to update log by ID in ${indexName} for user ${userId}, tracker ${trackerId}, logId ${logId}: ${error.message}`);
  }
}

/**
 * logs will be array of logs in same format as of db
 */
async function postLogUpsert(userId, trackerIdLogsMapping) {
  try {
    const defaultTrackers = await trackersService.getAllDefaultTrackers(userId);
    const UTCOffsetMin = await getUTCOffsetValue(userId);

    const trackerIdDeviceId = {};
    for (const trackerId in trackerIdLogsMapping) {
      const logs = trackerIdLogsMapping[trackerId];
      for (const log of logs) {
        const date = getLocalDateString(log.timestamp, UTCOffsetMin);
        let computeTargetAchievementFlag = checkDeviceConditions(
          log.deviceId,
          trackerId,
          defaultTrackers
        );
        if (computeTargetAchievementFlag) {
          if (!trackerIdDeviceId[date]) {
            trackerIdDeviceId[date] = { [trackerId]: log.deviceId };
          } else {
            trackerIdDeviceId[date][trackerId] = log.deviceId;
          }
        }
      }
    }

    const dates = Object.keys(trackerIdDeviceId);
    logger.info(`Processing post log upsert for user ${userId}: ${dates.length} dates to process [${dates.join(', ')}]`);
    const promises = Object.keys(trackerIdDeviceId).map(async (date) => {
      const message = JSON.stringify({
        userId,
        date,
        trackerIdDeviceIdMapping: trackerIdDeviceId[date],
        time: new Date().getTime(), // unique identifier
      });
      try {
        const isSent = await sendTargetComputationSQSMessage(message);
        return { date, success: isSent };
      } catch (err) {
        const trackerIds = Object.keys(trackerIdDeviceId[date]);
        logger.warn(`Error sending SQS message for target computation for user ${userId}, date ${date}, trackers [${trackerIds.join(', ')}]: ${err.message}`);
        return { date, success: false };
      }
    });

    const data = await Promise.all(promises);

    // Count successful and failed SQS messages
    const successCount = data.filter(item => item.success).length;
    const failureCount = data.length - successCount;

    const processedDates = data.map(item => item.date);
    logger.info(`Post log upsert SQS messages completed for user ${userId}: ${successCount} successful, ${failureCount} failed out of ${data.length} messages. Dates processed: [${processedDates.join(', ')}]`);
    return true;
  } catch (error) {
    logger.warn(`Error in postLogUpsert for user ${userId}: ${error.message}\nStack: ${error.stack || 'No stack trace available'}`);
    return false;
  }
}

function checkDeviceConditions(logDeviceId, logTrackerId, defaultTrackers) {
  let isDefaultTrackerLog = false;
  if (defaultTrackers?.defaultDevices?.length) {
    const dd = defaultTrackers?.defaultDevices.filter(
      (dd) => dd.trackerId == logTrackerId && dd.deviceId == logDeviceId
    );
    if (dd && dd.length) isDefaultTrackerLog = true;
  }

  // Check if it's a manual entry log
  const isManualLog = logDeviceId == MANUAL_ENTRY_DEVICE_ID;

  return isDefaultTrackerLog || isManualLog;
}

/**
 * Bulk insert multiple documents for a specific tracker
 * @param {string} trackerId - The tracker ID
 * @param {string} indexName - The index name
 * @param {Array} documents - Array of documents to insert
 * @returns {Promise<Array>} - Array of inserted document IDs
 */
async function bulkInsertData(trackerId, indexName, documents) {
  if (!documents || documents.length === 0) {
    return [];
  }

  // Handle special cases for exercise and mindfulness logs
  const numTrackerId = Number(trackerId);
  if (numTrackerId === 4 || numTrackerId === 24) {
    // Process exercise or mindfulness logs in parallel
    const processPromises = documents.map(async (document) => {
      const defaultDeviceId = await devicesService.getDefaultDeviceIdByTrackerId(
        document.userId,
        trackerId
      );

      if (defaultDeviceId && defaultDeviceId == document.deviceId) {
        if (numTrackerId === 4) {
          await exerciseLogs.addLog(document.userId, document);
        } else if (numTrackerId === 24) {
          await mindfulnessLogs.addLog(document.userId, document);
        }
      }
    });

    await Promise.all(processPromises);
  }

  // Bulk insert all documents
  return await utils.bulkInsertData(indexName, documents);
}

/**
 * Bulk upsert multiple documents
 * @param {string} trackerId - The tracker ID
 * @param {string} indexName - The index name
 * @param {Array} documents - Array of documents to upsert
 * @returns {Promise<Object>} - Object mapping document indices to their IDs
 */
async function bulkUpsertData(trackerId, indexName, documents) {
  if (!documents || documents.length === 0) {
    return {};
  }

  const numTrackerId = Number(trackerId);
  const resultMap = {};

  // Process special cases for exercise and mindfulness logs
  if (numTrackerId === 4 || numTrackerId === 24) {
    const processPromises = documents.map(async (document) => {
      if (numTrackerId === 4) {
        await exerciseLogs.addLog(document.userId, document);
      } else if (numTrackerId === 24) {
        await mindfulnessLogs.addLog(document.userId, document);
      }
    });

    await Promise.all(processPromises);
  }

  // Group documents by userId for more efficient searching
  const userGroups = {};
  documents.forEach((doc, index) => {
    const userId = doc.userId;
    if (!userGroups[userId]) {
      userGroups[userId] = [];
    }
    userGroups[userId].push({ doc, index });
  });

  // Process each user group
  for (const userId in userGroups) {
    const userDocs = userGroups[userId];
    const docsByLogId = {};
    const docsByTimestamp = {};
    const docsToInsert = [];

    // Organize documents by logId and timestamp
    userDocs.forEach(({ doc, index }) => {
      if (doc.logId) {
        if (!docsByLogId[doc.logId]) {
          docsByLogId[doc.logId] = [];
        }
        docsByLogId[doc.logId].push({ doc, index });
      } else {
        const comparator = numTrackerId === sleepLogTrackerId ? "startTime" : "timestamp";
        const timestamp = doc[comparator];
        if (!docsByTimestamp[timestamp]) {
          docsByTimestamp[timestamp] = [];
        }
        docsByTimestamp[timestamp].push({ doc, index, deviceId: doc.deviceId });
      }
    });

    // Process documents with logId
    if (Object.keys(docsByLogId).length > 0) {
      const logIds = Object.keys(docsByLogId);
      const response = await executeWithRetry(
        async (client) => {
          return await client.search({
            index: indexName,
            body: {
              query: {
                bool: {
                  must: [
                    { match: { "userId.keyword": userId } },
                    { terms: { "logId.keyword": logIds } }
                  ]
                }
              },
              size: logIds.length
            }
          });
        },
        {
          description: `bulkUpsertData search by logIds in ${indexName}`,
          maxRetries: 3
        }
      );

      const hits = response.body?.hits?.hits || [];
      const foundLogIds = {};

      // Process found documents for update
      const updateOperations = [];
      hits.forEach(hit => {
        const logId = hit._source.logId;
        foundLogIds[logId] = hit._id;

        docsByLogId[logId].forEach(({ doc, index }) => {
          updateOperations.push({
            action: { update: { _index: indexName, _id: hit._id } },
            document: { doc }
          });
          resultMap[index] = hit._id;
        });
      });

      // Find documents that need to be inserted
      for (const logId in docsByLogId) {
        if (!foundLogIds[logId]) {
          docsByLogId[logId].forEach(({ doc, index }) => {
            docsToInsert.push({ doc, index });
          });
        }
      }

      // Execute bulk updates if any
      if (updateOperations.length > 0) {
        await executeBulkWithRetry(updateOperations, {
          description: `Bulk update by logId in ${indexName}`
        });
      }
    }

    // Process documents with timestamp
    for (const timestamp in docsByTimestamp) {
      const docsWithTimestamp = docsByTimestamp[timestamp];
      const deviceIds = [...new Set(docsWithTimestamp.map(item => item.deviceId))];

      // For each device ID, find matching documents
      for (const deviceId of deviceIds) {
        const docsForDevice = docsWithTimestamp.filter(item => item.deviceId === deviceId);
        if (docsForDevice.length === 0) continue;

        const comparator = numTrackerId === sleepLogTrackerId ? "startTime" : "timestamp";
        const bufferMinutes = 1;
        const timestampDate = new Date(timestamp);
        const timeBuffer = bufferMinutes * 60 * 1000;

        const response = await executeWithRetry(
          async (client) => {
            return await client.search({
              index: indexName,
              body: {
                query: {
                  bool: {
                    must: [
                      { match: { "userId.keyword": userId } },
                      { match: { deviceId } },
                      {
                        range: {
                          [comparator]: {
                            gte: new Date(timestampDate.getTime() - timeBuffer).toISOString(),
                            lte: new Date(timestampDate.getTime() + timeBuffer).toISOString(),
                          }
                        }
                      }
                    ]
                  }
                }
              }
            });
          },
          {
            description: `bulkUpsertData search by timestamp in ${indexName}`,
            maxRetries: 3
          }
        );

        const hits = response.body?.hits?.hits || [];

        if (hits.length > 0) {
          // Update existing documents
          const updateOperations = [];
          docsForDevice.forEach(({ doc, index }) => {
            // Use the first matching document (closest match)
            const hit = hits[0];
            updateOperations.push({
              action: { update: { _index: indexName, _id: hit._id } },
              document: { doc }
            });
            resultMap[index] = hit._id;
          });

          if (updateOperations.length > 0) {
            await executeBulkWithRetry(updateOperations, {
              description: `Bulk update by timestamp in ${indexName}`
            });
          }
        } else {
          // No matching documents found, add to insert list
          docsForDevice.forEach(item => {
            docsToInsert.push(item);
          });
        }
      }
    }

    // Insert documents that weren't found for update
    if (docsToInsert.length > 0) {
      const docsToInsertArray = docsToInsert.map(item => item.doc);
      const insertedIds = await utils.bulkInsertData(indexName, docsToInsertArray);

      // Map inserted IDs back to original indices
      docsToInsert.forEach(({ index }, i) => {
        if (i < insertedIds.length) {
          resultMap[index] = insertedIds[i];
        }
      });
    }
  }

  return resultMap;
}

module.exports = {
  trackerMap,
  upsertData,
  insertData,
  postLogUpsert,
  updateLogById,
  getLogByTimestampByDeviceId,
  bulkInsertData,
  bulkUpsertData
};

const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const { executeWithRetry, executeBulkWithRetry, parseOpenSearchError } = require("../utils/elasticSearchOperations");

indexes = Object.keys(config.INDEX).map(k => config.INDEX[k])

async function insertData(indexName, document) {
  try {
    const response = await executeWithRetry(
      async (client) => {
        return await client.index({
          index: indexName,
          body: document,
        });
      },
      {
        description: `Insert data into ${indexName}`,
        maxRetries: 3
      }
    );
    return response.body?._id;
  } catch (error) {
    const errorInfo = parseOpenSearchError(error);

    logger.error(`Failed to insert document into ${indexName}`, {
      indexName,
      userId: document?.userId,
      timestamp: document?.timestamp,
      error: errorInfo.message,
      errorType: errorInfo.type,
      statusCode: errorInfo.statusCode,
      details: errorInfo.details,
      rootCause: errorInfo.rootCause,
      isRetryable: errorInfo.isRetryable
    });

    // Rethrow the error to allow the caller to handle it
    throw error;
  }
}

/**
 * Insert multiple documents in bulk
 * @param {string} indexName - The index to insert into
 * @param {Array} documents - Array of documents to insert
 * @param {Object} options - Options for the bulk operation
 * @returns {Promise<Array>} - Array of inserted document IDs
 */
async function bulkInsertData(indexName, documents, options = {}) {
  try {
    if (!documents || documents.length === 0) {
      return [];
    }

    // Prepare operations for bulk insert
    const operations = documents.map(doc => ({
      action: { index: { _index: indexName } },
      document: doc
    }));

    const results = await executeBulkWithRetry(operations, {
      ...options,
      description: `Bulk insert into ${indexName}`
    });

    // Extract successful IDs
    return results
      .filter(result => result.success)
      .map(result => result.id);
  } catch (error) {
    const errorInfo = parseOpenSearchError(error);

    logger.error(`Failed to bulk insert documents into ${indexName}`, {
      indexName,
      documentCount: documents?.length || 0,
      error: errorInfo.message,
      errorType: errorInfo.type,
      statusCode: errorInfo.statusCode,
      details: errorInfo.details,
      rootCause: errorInfo.rootCause,
      isRetryable: errorInfo.isRetryable
    });

    throw error;
  }
}

module.exports = {
  insertData,
  bulkInsertData
};

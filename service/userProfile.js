const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");

// hardcoded for IST, incase value not found in DB
const UTCOffsetMin = 330;

const indexName = config.INDEX.userProfiles;

async function getUTCOffsetValue(userId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    _source: ["UTCOffsetMin"],
    body: {query: {bool: {must: [{ match: { "userId.keyword": userId } }]}}, size: 1},
  });

  const data = response.body?.hits?.hits[0]?._source || null;
  return data?.UTCOffsetMin || UTCOffsetMin;
}

module.exports = {
  getUTCOffsetValue,
}

const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const utils = require("./utils.js");

const indexName = config.INDEX.user;

async function insertUser(document) {
  return await utils.insertData(indexName, document)
}

async function upsertUserDetails(userId, body, sourceId) {
  const client = getClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [{ match: { "userId.keyword": userId } }, { match: { sourceId } }],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertUser({ ...body, userId });
  }
  delete body?.createdAt;
  body.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...body,
      },
    },
  });

  logger.debug({ message: "Updated user details", userId, body });
  return updateResponse.body;
}

const userService = {
  upsertUserDetails,
};

module.exports = { userService };

const { getClient } = require("../utils/connection");
const { config } = require("../environment/index");
const indexName = config.INDEX.trackers;

async function getAllDefaultTrackers(userId) {
  const client = await getClient();

  const response = await client.search({
    index: indexName,
    body: {
      query: { match: { "userId.keyword": userId } },
    },
  });

  const data = response.body?.hits?.hits[0]?._source || {};
  return data;
}

const trackersService = {
  getAllDefaultTrackers,
};

module.exports = {
  trackersService,
};

const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { sign } = aws4;
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

let OpenSearchClient;
let lastCredentialsRefresh = 0;
const CREDENTIALS_REFRESH_INTERVAL = 10 * 60 * 1000; // 10 minutes

function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es";
      request.region = region;
      request.headers = request.headers || {};
      request.headers["host"] = request.hostname;

      return sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

async function createClient() {
  try {
    logger.info("Creating OpenSearch client with fresh credentials", {
      osHost: config.OS_HOST,
      region: config.AWS.REGION
    });

    const credentials = await defaultProvider()();
    lastCredentialsRefresh = Date.now();

    logger.info("Successfully obtained AWS credentials", {
      AccessKey: credentials.accessKeyId,
      SecretKey: credentials.secretAccessKey,
      SessionToken: credentials.sessionToken,
      credentialsRefreshedAt: new Date().toISOString()
    });

    if (!config.OS_HOST) {
      throw new Error('OpenSearch host (OS_HOST) is not configured');
    }

    if (!config.AWS.REGION) {
      throw new Error('AWS region (REGION) is not configured');
    }

    OpenSearchClient = new Client({
      ...createAwsConnector(credentials, config.AWS.REGION),
      node: config.OS_HOST,
      requestTimeout: 30000, // 30 seconds timeout
      maxRetries: 3,         // Retry failed requests up to 3 times
      compression: 'gzip',   // Enable compression for better performance
      ssl: {
        rejectUnauthorized: true
      }
    });

    // Test the connection with detailed error reporting
    try {
      const pingResponse = await OpenSearchClient.ping();
      logger.info("OpenSearch client created and connection verified successfully", {
        cluster: config.OS_HOST,
        responseTime: pingResponse.meta?.request?.params?.timeout || 'unknown'
      });
    } catch (pingError) {
      // Parse the ping error for better diagnostics
      const errorDetails = {
        error: pingError.message,
        statusCode: pingError.meta?.statusCode,
        headers: pingError.meta?.headers,
        requestId: pingError.meta?.request?.id
      };

      // Try to parse the response body for more details
      if (pingError.meta?.body) {
        try {
          const body = typeof pingError.meta.body === 'string'
            ? JSON.parse(pingError.meta.body)
            : pingError.meta.body;

          if (body.error) {
            errorDetails.errorType = body.error.type;
            errorDetails.errorReason = body.error.reason;
            errorDetails.errorDetails = body.error.caused_by?.reason;
          }
        } catch (parseError) {
          errorDetails.rawBody = pingError.meta.body;
        }
      }

      // Provide specific guidance based on the error
      if (pingError.meta?.statusCode === 403) {
        errorDetails.suggestion = 'Check IAM permissions for OpenSearch access. The credentials may lack necessary permissions.';
        errorDetails.requiredPermissions = [
          'es:ESHttpGet',
          'es:ESHttpPost',
          'es:ESHttpPut',
          'es:ESHttpDelete'
        ];
      } else if (pingError.meta?.statusCode === 401) {
        errorDetails.suggestion = 'Authentication failed. Check AWS credentials and signature.';
      } else if (pingError.meta?.statusCode >= 500) {
        errorDetails.suggestion = 'OpenSearch cluster appears to be experiencing issues.';
      }

      logger.warn("OpenSearch client created but ping failed", errorDetails);

      // Don't throw here as the client might still work for actual operations
      // But log additional context for debugging
      logger.debug("OpenSearch connection context", {
        osHost: config.OS_HOST,
        region: config.AWS.REGION,
        hasCredentials: !!credentials.accessKeyId,
        credentialType: credentials.sessionToken ? 'temporary' : 'permanent'
      });
    }

    return OpenSearchClient;
  } catch (error) {
    logger.error("Failed to create OpenSearch client", {
      error: error.message,
      stack: error.stack,
      osHost: config.OS_HOST,
      region: config.AWS.REGION
    });
    throw error;
  }
}

function getClient() {
  // If client doesn't exist, we need to create it first
  if (!OpenSearchClient) {
    logger.warn("OpenSearch client not initialized. This should not happen in normal operation.");
    return null;
  }

  // Check if we need to refresh the client
  const now = Date.now();
  if (now - lastCredentialsRefresh > CREDENTIALS_REFRESH_INTERVAL) {
    logger.info("Credentials are stale, refreshing OpenSearch client", {
      lastRefresh: new Date(lastCredentialsRefresh).toISOString(),
      refreshIntervalMinutes: CREDENTIALS_REFRESH_INTERVAL / (60 * 1000)
    });

    // Don't await here to avoid blocking current operations
    createClient().catch(err => {
      logger.error("Failed to refresh OpenSearch client credentials", {
        error: err.message,
        stack: err.stack
      });
    });
  }

  return OpenSearchClient;
}

module.exports = {
  getClient,
  createClient,
};

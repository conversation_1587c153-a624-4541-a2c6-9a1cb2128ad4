const { Client, Connection } = require("@opensearch-project/opensearch");
const { defaultProvider } = require("@aws-sdk/credential-provider-node");
const aws4 = require("aws4");
const { sign } = aws4;
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

let OpenSearchClient;
let lastCredentialsRefresh = 0;
const CREDENTIALS_REFRESH_INTERVAL = 10 * 60 * 1000; // 10 minutes

function createAwsConnector(credentials, region) {
  class AmazonConnection extends Connection {
    buildRequestObject(params) {
      const request = super.buildRequestObject(params);
      request.service = "es";
      request.region = region;
      request.headers = request.headers || {};
      request.headers["host"] = request.hostname;

      return sign(request, credentials);
    }
  }
  return {
    Connection: AmazonConnection,
  };
}

async function createClient() {
  try {
    logger.info("Creating OpenSearch client with fresh credentials");
    const credentials = await defaultProvider()();
    lastCredentialsRefresh = Date.now();

    logger.info({
      message: "Got AWS credentials",
      hasAccessKey: !!credentials.accessKeyId,
      hasSecretKey: !!credentials.secretAccessKey,
      hasSessionToken: !!credentials.sessionToken,
      timestamp: new Date().toISOString()
    });

    OpenSearchClient = new Client({
      ...createAwsConnector(credentials, config.REGION),
      node: config.OS_HOST,
      requestTimeout: 30000, // 30 seconds timeout
      maxRetries: 3,         // Retry failed requests up to 3 times
    });

    return OpenSearchClient;
  } catch (error) {
    logger.error("Failed to create OpenSearch client", error);
    throw error;
  }
}

function getClient() {
  // Check if we need to refresh the client
  const now = Date.now();
  if (now - lastCredentialsRefresh > CREDENTIALS_REFRESH_INTERVAL) {
    logger.info("Refreshing OpenSearch client credentials");
    // Don't await here to avoid blocking
    createClient().catch(err => {
      logger.error("Failed to refresh OpenSearch client", err);
    });
  }

  return OpenSearchClient;
}

module.exports = {
  getClient,
  createClient,
};

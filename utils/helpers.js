const crypto = require('crypto');

const deepCopy = (originalObject) => {
  return JSON.parse(JSON.stringify(originalObject))
}

// timestamp is ISO format
function getLocalDateString(timestamp, UTCOffsetMin) {
  const localDate = new Date(new Date(timestamp).getTime() + UTCOffsetMin * 60000);
  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');
  const dateString = `${year}-${month}-${day}`;
  return dateString;
}

function isEmpty(value) {
  if (typeof value === "object" && value !== null) {
    return Array.isArray(value) ? value.length === 0 : Object.keys(value).length === 0;
  }
  return false;
}

function checkIfAnyEmpty(obj) {
  for (let key in obj) {
    if (isEmpty(obj[key])) {
      return true; // Return true if any property is empty
    }
  }
  return false; // If none of the properties are empty, return false
}

/**
 * Generate OpenSearch _id using SHA-1 hash of raw string.
 * @param {string} rawString
 * @returns {string} SHA-1 hash as _id e.g. 2f16d0e7075b4765bcbfcd278a520c5e45f92e8c
 */
function encryptString(rawString) {
  return crypto.createHash('sha1').update(rawString).digest('hex');
}

module.exports = {
  deepCopy,
  checkIfAnyEmpty,
  getLocalDateString,
  encryptString
};

const { getClient, createClient } = require('./connection');
const { log: logger } = require('./logger');

/**
 * Execute an Elasticsearch operation with retries
 * @param {Function} operation - Function that takes a client and returns a promise
 * @param {Object} options - Options for the operation
 * @returns {Promise} - Result of the operation
 */
async function executeWithRetry(operation, options = {}) {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    description = 'Elasticsearch operation'
  } = options;

  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const client = getClient();
      return await operation(client);
    } catch (error) {
      lastError = error;

      // Check if this is a signature expiration error
      const isSignatureExpired =
        error.message?.includes('Signature expired') ||
        error.meta?.body?.message?.includes('Signature expired');

      // If it's the last attempt or not a retryable error, throw
      if (attempt === maxRetries || (!isSignatureExpired && !isRetryableError(error))) {
        logger.error(`${description} failed after ${attempt + 1} attempts`, {
          error: error.message,
          stack: error.stack
        });
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(2, attempt);

      logger.warn(`${description} failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms`, {
        error: error.message
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));

      // If it's a signature expiration error, force a client refresh
      if (isSignatureExpired) {
        logger.info("Forcing client refresh due to signature expiration");
        await createClient();
      }
    }
  }

  throw lastError;
}

/**
 * Execute a bulk operation with batching and retries
 * @param {Array} operations - Array of operations to perform in bulk
 * @param {Object} options - Options for the bulk operation
 * @returns {Promise<Array>} - Array of operation results
 */
async function executeBulkWithRetry(operations, options = {}) {
  const {
    batchSize = 500,
    maxRetries = 3,
    initialDelay = 1000,
    description = 'OpenSearch bulk operation'
  } = options;

  if (!operations || operations.length === 0) {
    return [];
  }

  // Split operations into batches
  const batches = [];
  for (let i = 0; i < operations.length; i += batchSize) {
    batches.push(operations.slice(i, i + batchSize));
  }

  logger.info(`Starting bulk operation: Processing ${operations.length} items in ${batches.length} batches (max ${batchSize} items per batch)`);

  // Process each batch with retry logic
  const results = [];
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const batchDesc = `${description} (batch ${batchIndex + 1}/${batches.length})`;

    try {
      const batchResults = await executeWithRetry(
        async (client) => {
          const bulkBody = [];

          // Build the bulk request body
          batch.forEach(op => {
            bulkBody.push(op.action);
            if (op.document) {
              bulkBody.push(op.document);
            }
          });

          const response = await client.bulk({
            body: bulkBody
          });

          // Process the response to extract IDs and check for errors
          const items = response.body.items || [];
          return items.map((item, index) => {
            const actionType = Object.keys(item)[0]; // index, create, update, delete
            const result = item[actionType];

            if (result.error) {
              logger.warn(`Bulk operation error for item ${index}: ${JSON.stringify(result.error).substring(0, 200)}...`);
              return {
                success: false,
                error: result.error,
                operation: batch[index]
              };
            }

            return {
              success: true,
              id: result._id,
              operation: batch[index]
            };
          });
        },
        {
          description: batchDesc,
          maxRetries,
          initialDelay
        }
      );

      results.push(...batchResults);

      // Count successful and failed operations
      const successCount = batchResults.filter(r => r.success).length;
      const failureCount = batchResults.length - successCount;

      logger.info(`${batchDesc} completed: ${successCount} successful, ${failureCount} failed out of ${batchResults.length} operations`);
    } catch (error) {
      logger.error(`${batchDesc} failed completely: ${error.message}\nStack: ${error.stack || 'No stack trace available'}`);
      // Add failed results for this batch
      batch.forEach(op => {
        results.push({
          success: false,
          error: error.message || 'Unknown error',
          operation: op
        });
      });
    }
  }

  return results;
}

/**
 * Check if an error is retryable
 * @param {Error} error - The error to check
 * @returns {boolean} - Whether the error is retryable
 */
function isRetryableError(error) {
  // Network errors, timeouts, and 5xx errors are retryable
  return (
    error.name === 'ConnectionError' ||
    error.name === 'TimeoutError' ||
    (error.meta?.statusCode >= 500 && error.meta?.statusCode < 600)
  );
}

module.exports = {
  executeWithRetry,
  executeBulkWithRetry
};

const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");
const { config } = require("../environment/index");
const { log: logger } = require("./logger");
const { checkIfAnyEmpty, deepCopy } = require("../utils/helpers");

// Create Lambda client with v3 SDK - let it use default credential resolution
const lambdaClient = new LambdaClient({
  region: "us-east-1",
  requestHandler: {
    // Configure connection pooling for better performance
    connectionTimeout: 5000,
    socketTimeout: 30000,
    maxRetries: 2
  }
});

let staticData = '{"targetsMap":{},"trackerMap":[],"devices":[],"sources":{},"sourcesArray":[],"sourceSummary":{},"sourceDevices":{},"devicesWithoutManualEntry":[],"sourcesDevicesMapping":{}}';

async function fetchStaticData() {
  try {
    const command = new InvokeCommand({
      FunctionName: config.AWS.TRACKERS_STATIC_DATA_LAMBDA,
      InvocationType: "RequestResponse",
      LogType: "None", // Reduce overhead by not fetching logs
      Qualifier: "provisioned",
    });

    const response = await lambdaClient.send(command);
    const payload = JSON.parse(new TextDecoder().decode(response.Payload));
    return payload.body;
  } catch (error) {
    logger.error(
      "Error while calling Lambda function",
      JSON.stringify(error)
    );
    logger.warn(`Failed to fetch staticData from trackers`);
    return null;
  }
}

async function getStaticData() {
  let staticDataJson = JSON.parse(staticData || "{}");
  if (!checkIfAnyEmpty(staticDataJson)) {
    return staticDataJson;
  }
  const responseString = await fetchStaticData();
  const response = JSON.parse(responseString);
  if (response?.success) {
    const { targetsMap, trackerMap, devices, sources } = response.data;
    const sourcesArray = await getStaticSourcesArray(sources);
    const sourceSummary = await getStaticSourceSummary(deepCopy(sources));
    const devicesWithoutManualEntry = await getStaticDevicesWithoutManualEntry(devices);
    const sourceDevices = await getStaticSourceDevices(devices);
    const sourcesDevicesMapping = await getStaticSourcesDevicesMapping(devices);
    const trackerDefaultTargetsMapping = await getStaticTrackerDefaultTargetsMapping(targetsMap);
    Object.assign(staticDataJson, {
      targetsMap,
      trackerMap,
      devices,
      sources,
      sourcesArray,
      sourceSummary,
      devicesWithoutManualEntry,
      sourceDevices,
      sourcesDevicesMapping,
      trackerDefaultTargetsMapping,
    });
    staticData = JSON.stringify(staticDataJson);
    logger.info(response?.message || `Successfully fetched static data`);
  } else {
    logger.warn(`Success: false, Failed to fetch staticData from trackers`);
  }
  return staticDataJson;
}

async function getStaticTargetsMap() {
  const staticData = await getStaticData();
  return staticData?.targetsMap || {};
}

async function getStaticTrackers() {
  const staticData = await getStaticData();
  return staticData?.trackerMap || [];
}

async function getStaticSources() {
  const staticData = await getStaticData();
  const sources = staticData?.sources || {};
  return sources;
}

async function getStaticDevices() {
  const staticData = await getStaticData();
  const devices = staticData?.devices || [];
  return devices;
}

// ----- modified jsons -----
async function getStaticSourcesArray(sources) {
  if (!sources) {
    const data = await getStaticData();
    return data?.sourcesArray || {};
  }
  const sourcesArray = Object.keys(sources).map((s) => sources[s]);
  return sourcesArray;
}

async function getStaticSourceSummary(sources) {
  if (!sources) {
    const data = await getStaticData();
    return data?.sourceSummary || {};
  }
  let sourceSummary = {};
  Object.keys(sources).map((sourceId) => {
    const id = sources[sourceId].id;
    sourceSummary[id] = sources[sourceId];
    delete sourceSummary[id].trackers;
  });
  return sourceSummary;
}

async function getStaticDevicesWithoutManualEntry(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.devicesWithoutManualEntry || {};
  }
  const devicesWithoutManualEntry = devices.filter(x => x.id != -1);
  return devicesWithoutManualEntry;
}

async function getStaticSourceDevices(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.sourceDevices || {};
  }
  const sourceDevices = devices.reduce((acc, dev) => {
    const sourceId = dev.sourceId.toString();
    acc[sourceId] = acc[sourceId] || [];
    acc[sourceId].push(dev);
    return acc;
  }, {});
  return sourceDevices;
}

async function getStaticSourcesDevicesMapping(devices) {
  if (!devices) {
    const data = await getStaticData();
    return data?.sourcesDevicesMapping || {};
  }
  const sourcesDevicesMapping = devices.reduce((obj, { sourceId, id }) => ((obj[sourceId] ||= []).push(id), obj), {});
  return sourcesDevicesMapping;
}

async function getStaticTrackerDefaultTargetsMapping(targetsMap) {
  if (!targetsMap) {
    const data = await getStaticData();
    return data?.trackerDefaultTargetsMapping || {};
  }
  let trackerDefaultTargetsMapping = {};
  Object.keys(targetsMap).map((targetId) => {
    const targetDoc = targetsMap[targetId];
    if (targetsMap[targetId]?.isDefault)
      trackerDefaultTargetsMapping[targetDoc.trackerIds[0]] = Number(targetId);
  });
  return trackerDefaultTargetsMapping;
}

module.exports = {
  getStaticData,
  staticData,
  getStaticTargetsMap,
  getStaticTrackers,
  getStaticSources,
  getStaticDevices,
  getStaticSourcesArray,
  getStaticSourceSummary,
  getStaticDevicesWithoutManualEntry,
  getStaticSourceDevices,
  getStaticSourcesDevicesMapping,
  getStaticTrackerDefaultTargetsMapping,
};

const AWS = require("aws-sdk");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");

AWS.config.update({ region: config.AWS.REGION });
const s3 = new AWS.S3({ region: config.AWS.REGION });
const sqs = new AWS.SQS({ apiVersion: "2012-11-05", region: config.AWS.REGION });

const S3_BUCKET = config.AWS.S3_BUCKET;
const TARGET_COMPUTATION_SQS_URL = config.AWS.TARGET_COMPUTATION_SQS_URL;

const getDataFromS3 = async (key) => {
  const params = {
    Bucket: S3_BUCKET,
    Key: key,
  };

  try {
    const data = await s3.getObject(params).promise();
    const parsedData = JSON.parse(data.Body.toString("utf-8"));
    logger.info(`Data fetched successfully from S3 Path: ${key}`);
    return parsedData;
  } catch (error) {
    logger.error(`Error fetching data from S3: ${key} - ${error.message}`);
    return {};
  }
};

async function sendTargetComputationSQSMessage(message) {
  const params = {
    MessageAttributes: {},
    MessageBody: message,
    QueueUrl: TARGET_COMPUTATION_SQS_URL,
    MessageGroupId: 'TargetComputation',
  };
  try {
    const resp = await sqs.sendMessage(params).promise();
    return true;
  } catch (err) {
    logger.info("Error sending message to Target Computation SQS", JSON.stringify(err));
    return false;
  }
}

module.exports = {
  getDataFromS3,
  sendTargetComputationSQSMessage,
};

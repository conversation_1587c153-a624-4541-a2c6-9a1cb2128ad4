# Environment Configuration

This directory contains the environment configuration for the TrackersIngestion Lambda function.

## Structure

- `index.js`: Main configuration loader that merges base configuration with environment-specific settings
- `development.js`: Development environment configuration
- `sandbox.js`: Sandbox/staging environment configuration
- `production.js`: Production environment configuration
- `env/`: Directory containing environment variable files
  - `.env.development`: Development environment variables
  - `.env.sandbox`: Sandbox environment variables
  - `.env.production`: Production environment variables
  - `.env.example`: Example environment variables template

## Environment Variables

The following environment variables are used:

| Variable | Description | Example |
|----------|-------------|---------|
| SERVER_URL | Server URL | https://services.healthtechgate.com |
| AWS_REGION | AWS Region | us-east-1 |
| AWS_S3_BUCKET | S3 Bucket Name | twentydeg |
| AWS_TRACKERS_STATIC_DATA_LAMBDA | Static Data Lambda Name | trackers-static |
| AWS_TARGET_COMPUTATION_SQS_URL | Target Computation SQS URL | https://sqs.region.amazonaws.com/account/queue.fifo |
| OS_HOST | OpenSearch Host | https://search-domain.region.es.amazonaws.com |
| X_API_KEY | API Key | your-api-key |

## Usage

### Local Development

1. Copy `.env.example` to `.env.development` and fill in the values:
   ```bash
   cp environment/env/.env.example environment/env/.env.development
   ```

2. Run the application with the development environment:
   ```bash
   npm run start:dev
   ```

### Deployment

1. Ensure your AWS credentials are configured:
   ```bash
   aws configure --profile dev-profile
   ```

2. Upload environment variables to AWS Secrets Manager:
   ```bash
   # For development
   npm run deploy:dev
   
   # For sandbox
   npm run deploy:sandbox
   
   # For production
   npm run deploy:prod
   ```

3. Deploy the Lambda function with the appropriate environment variables:
   ```bash
   # Example using AWS CLI
   aws lambda update-function-configuration \
     --function-name TrackersIngestion \
     --environment "Variables={NODE_ENV=production}"
   ```

## AWS Secrets Manager Integration

The environment variables are stored in AWS Secrets Manager for secure deployment. The script `scripts/upload-env-to-secrets.sh` uploads the environment variables to AWS Secrets Manager.

### Secret Names

- Development: `trackers-ingestion-development`
- Sandbox: `trackers-ingestion-sandbox`
- Production: `trackers-ingestion-production`

### Retrieving Secrets in Lambda

To retrieve secrets in the Lambda function, you can use the AWS SDK:

```javascript
const AWS = require('aws-sdk');
const secretsManager = new AWS.SecretsManager();

async function getSecrets() {
  const secretName = `trackers-ingestion-${process.env.NODE_ENV}`;
  const data = await secretsManager.getSecretValue({ SecretId: secretName }).promise();
  const secrets = JSON.parse(data.SecretString);
  
  // Set environment variables
  Object.entries(secrets).forEach(([key, value]) => {
    process.env[key] = value;
  });
}
```

## Best Practices

1. Never commit `.env.*` files to the repository (except `.env.example`)
2. Use different AWS profiles for different environments
3. Limit access to AWS Secrets Manager secrets based on IAM roles
4. Rotate API keys and other sensitive values regularly

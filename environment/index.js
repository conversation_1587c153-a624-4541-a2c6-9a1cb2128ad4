const nconf = require("nconf");
const _ = require("lodash");
const dotenv = require("dotenv");
const path = require("path");
const fs = require("fs");

// Determine environment
const nodeEnv = process.env.NODE_ENV || "development";

// Load environment variables from .env file
const envPath = path.resolve(__dirname, `env/.env.${nodeEnv}`);
if (fs.existsSync(envPath)) {
  console.log(`Loading environment variables from ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.warn(`No .env file found at ${envPath}, using process.env only`);
}

// Configure nconf to use environment variables
nconf
  .argv()
  .env()
  .defaults({
    NODE_ENV: nodeEnv,
    X_API_KEY: process.env.X_API_KEY || "",
  });

// Base configuration
const all = {
  env: nodeEnv,
  xAPIKey: nconf.get("X_API_KEY"),
  INDEX: {
    water: "water",
    activity: "activity",
    activitySummary: "activity_summary",
    sleep: "sleep",
    bp: "bp",
    bg: "bg",
    egvs: "egvs",
    spo2: "spo2",
    heartRate: "heart_rate",
    restingHeartRate: "resting_heart_rate",
    hrv: "hrv",
    vo2: "vo2",
    ecg: "ecg",
    height: "height",
    weight: "weight",
    fat: "fat",
    bmi: "bmi",
    temp: "temp",
    waistSize: "waist_size",
    hipSize: "hip_size",
    chestSize: "chest_size",
    armSize: "arm_size",
    quadSize: "quad_size",
    mindfulness: "mindfulness",

    user: "user",
    trackers: "trackers",
    userProfiles: "user_profiles",
  },
};

// Merge with environment-specific configuration
const config = _.merge(all, require("./" + all.env + ".js") || {});

module.exports = { config };
